# AI智能任务系统记忆功能需求

## 背景

当前AI智能任务系统已具备基础的任务管理和AI对话能力，但AI无法记住用户之前告诉它的信息，每次对话都是全新开始。通过添加简单的记忆功能，AI可以记住用户主动分享的信息，在后续对话中提供更个性化的建议。

## 需求

### 功能需求

#### 1. 记忆存储功能
- **用户主动输入记忆**：用户可以主动告诉AI需要记住的信息
- **AI识别记忆内容**：AI能够识别用户想要记忆的内容并保存
- **记忆持久化存储**：将记忆内容保存到数据库中

#### 2. 记忆应用功能
- **自动加载记忆**：每次AI对话时自动从数据库加载用户记忆
- **记忆融入对话**：将记忆内容作为上下文信息加入AI提示词
- **个性化回复**：基于记忆内容提供个性化的回复和建议

#### 3. 记忆管理功能
- **记忆查看**：用户可以查看当前存储的所有记忆
- **记忆删除**：用户可以删除不需要的记忆
- **记忆编辑**：用户可以修改已存储的记忆内容

### 非功能需求

#### 1. 性能要求
- 记忆查询响应时间 < 200ms
- 记忆存储操作 < 500ms
- 不影响AI对话的响应速度

#### 2. 数据安全
- 记忆数据安全存储
- 用户隐私保护
- 按用户隔离存储

#### 3. 用户体验
- 记忆功能简单易用
- 记忆应用过程对用户透明
- 提供清晰的记忆管理界面

## 技术方案

### 数据结构设计

#### 记忆数据表 (Memory)
```typescript
interface Memory {
  _id: string
  userId: string // 用户ID
  content: string // 记忆内容（纯文本）
  createTime: string // 创建时间
  updateTime: string // 更新时间
}
```

#### 数据库表结构
```sql
CREATE TABLE memory (
  _id TEXT PRIMARY KEY,
  userId TEXT NOT NULL,
  content TEXT NOT NULL,
  createTime TEXT NOT NULL,
  updateTime TEXT NOT NULL
);

-- 索引
CREATE INDEX idx_memory_userId ON memory(userId);
CREATE INDEX idx_memory_createTime ON memory(createTime);
```

### 实现架构

#### 1. 记忆管理模块
```javascript
// 记忆相关API
module.exports = {
  // 添加记忆
  async addMemory(content) {
    const userId = this.getUniIdToken().uid
    // 保存到数据库
  },

  // 获取用户所有记忆
  async getMemories() {
    const userId = this.getUniIdToken().uid
    // 从数据库查询
  },

  // 删除记忆
  async deleteMemory(memoryId) {
    const userId = this.getUniIdToken().uid
    // 从数据库删除
  }
}
```

#### 2. AI对话集成
```javascript
// 在 chatStreamSSE 中集成记忆
async chatStreamSSE({ message, messages, channel }) {
  const userId = this.getUniIdToken().uid

  // 1. 检查是否是记忆指令
  if (isMemoryCommand(message)) {
    return await handleMemoryCommand(userId, message)
  }

  // 2. 加载用户记忆
  const memories = await getUserMemories(userId)

  // 3. 构建包含记忆的系统提示词
  const systemPrompt = buildSystemPromptWithMemories(memories)

  // 4. 正常进行AI对话
  // ...
}
```

### 应用场景实现

#### 场景1：用户主动添加记忆
```
用户输入：请记住，我最近的目标是提升编程技能，特别是Vue.js和TypeScript
AI识别：这是一个记忆指令
AI操作：将"我最近的目标是提升编程技能，特别是Vue.js和TypeScript"保存到数据库
AI回复：好的，我已经记住了您的目标。
```

#### 场景2：AI在对话中应用记忆
```
用户输入：帮我创建一个学习任务
AI处理：
1. 从数据库加载用户记忆
2. 发现记忆："我最近的目标是提升编程技能，特别是Vue.js和TypeScript"
3. 将记忆加入系统提示词
4. 基于记忆提供个性化建议

AI回复：根据您提升编程技能的目标，我建议创建一个Vue.js学习任务。您想学习Vue.js的哪个方面呢？比如组合式API、状态管理或者TypeScript集成？
```

#### 场景3：记忆管理
```
用户输入：我想看看你记住了什么
AI操作：查询数据库中该用户的所有记忆
AI回复：我记住了以下内容：
1. 您最近的目标是提升编程技能，特别是Vue.js和TypeScript
2. 您喜欢在周末进行学习
3. 您偏好使用番茄工作法

用户输入：删除第2条记忆
AI操作：从数据库删除对应记忆
AI回复：已删除关于周末学习偏好的记忆。
```

## 技术实现细节

### 1. 记忆识别逻辑
```javascript
function isMemoryCommand(message) {
  const memoryKeywords = [
    '请记住', '记住', '记录一下', '保存这个信息',
    '我要告诉你', '你需要知道', '重要信息'
  ]

  return memoryKeywords.some(keyword =>
    message.toLowerCase().includes(keyword.toLowerCase())
  )
}

function extractMemoryContent(message) {
  // 移除记忆指令词，提取实际内容
  const patterns = [
    /请记住[，：:]\s*(.*)/,
    /记住[，：:]\s*(.*)/,
    /记录一下[，：:]\s*(.*)/
  ]

  for (const pattern of patterns) {
    const match = message.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  return message // 如果没有匹配到模式，返回原始消息
}
```

### 2. 记忆加载和应用
```javascript
async function getUserMemories(userId) {
  const db = uniCloud.database()
  const { data } = await db.collection('memory')
    .where({ userId })
    .orderBy('createTime', 'desc')
    .limit(10) // 限制最近10条记忆
    .get()

  return data
}

function buildSystemPromptWithMemories(memories) {
  if (!memories || memories.length === 0) {
    return '你是一个AI助手，帮助用户管理任务。'
  }

  const memoryText = memories
    .map(m => `- ${m.content}`)
    .join('\n')

  return `你是一个AI助手，帮助用户管理任务。

用户之前告诉过你以下信息，请在回答时考虑这些背景：
${memoryText}

请基于这些信息提供个性化的建议和回复。`
}
```

### 3. 完整的云函数集成
```javascript
// uniCloud-aliyun/cloudfunctions/ai/index.obj.js
module.exports = {
  // 现有的 chatStreamSSE 方法修改
  async chatStreamSSE({ message, messages, channel }) {
    const userId = this.getUniIdToken().uid

    try {
      // 1. 检查是否是记忆指令
      if (isMemoryCommand(message)) {
        const memoryContent = extractMemoryContent(message)
        await this.addMemory(memoryContent)

        // 发送成功响应
        channel.write({
          type: 'message',
          data: { content: '好的，我已经记住了这个信息。' }
        })
        return { errCode: 0, errMsg: 'success' }
      }

      // 2. 加载用户记忆
      const memories = await getUserMemories(userId)

      // 3. 构建包含记忆的系统提示词
      const systemPrompt = buildSystemPromptWithMemories(memories)

      // 4. 正常进行AI对话（使用修改后的系统提示词）
      // ... 现有的AI对话逻辑

    } catch (error) {
      console.error('记忆功能错误:', error)
      // 继续正常的AI对话流程
    }
  },

  // 添加记忆
  async addMemory(content) {
    const userId = this.getUniIdToken().uid
    if (!userId) {
      return { errCode: 401, errMsg: '用户未登录' }
    }

    const db = uniCloud.database()
    const now = new Date().toISOString()

    await db.collection('memory').add({
      _id: db.command.uuid(),
      userId,
      content,
      createTime: now,
      updateTime: now
    })

    return { errCode: 0, errMsg: 'success' }
  },

  // 获取记忆列表
  async getMemories() {
    const userId = this.getUniIdToken().uid
    if (!userId) {
      return { errCode: 401, errMsg: '用户未登录' }
    }

    const db = uniCloud.database()
    const { data } = await db.collection('memory')
      .where({ userId })
      .orderBy('createTime', 'desc')
      .get()

    return { errCode: 0, errMsg: 'success', data }
  },

  // 删除记忆
  async deleteMemory(memoryId) {
    const userId = this.getUniIdToken().uid
    if (!userId) {
      return { errCode: 401, errMsg: '用户未登录' }
    }

    const db = uniCloud.database()
    await db.collection('memory')
      .where({ _id: memoryId, userId })
      .remove()

    return { errCode: 0, errMsg: 'success' }
  }
}
```

## 实施计划

### 第一阶段：核心功能开发 (1周)
1. **数据库设计**
   - 创建Memory表
   - 添加必要索引

2. **后端API开发**
   - 记忆CRUD操作
   - chatStreamSSE集成记忆功能

3. **基础测试**
   - 记忆存储和读取测试
   - AI对话集成测试

### 第二阶段：前端界面 (1周)
1. **记忆管理界面**
   - 记忆列表展示
   - 记忆删除功能

2. **AI对话优化**
   - 记忆指令识别提示
   - 个性化回复展示

### 第三阶段：优化和部署 (0.5周)
1. **性能优化**
   - 记忆查询优化
   - 缓存机制

2. **测试和部署**
   - 完整功能测试
   - 生产环境部署

## 成功指标

- 记忆存储成功率 > 99%
- 记忆查询响应时间 < 200ms
- AI对话个性化程度提升
- 用户满意度提升
